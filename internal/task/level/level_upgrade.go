package level

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

type LevelUpgradeTask struct{}

func NewLevelUpgradeTask() *LevelUpgradeTask {
	return &LevelUpgradeTask{}
}

// UpgradeUserLevels upgrades user levels daily at UTC 0:00 based on AgentLevel table
// Upgrade condition: current level assessment volume = sum of all valid transaction volumes in the past 30 days, excluding today
// Downgrade condition: 60-day grace period mechanism with 3 phases:
// Phase 1 (Days 1-30): Observation period - user below level requirements, no level change
// Phase 2 (Days 31-60): Warning period - system sends warning notification
// Phase 3 (Day 61): Downgrade execution - automatic downgrade to highest qualified level
// Note: Downgrade mechanism is controlled by system configuration and will be disabled for the first 6 months after launch
func (t *LevelUpgradeTask) UpgradeUserLevels() {
	global.GVA_LOG.Info("Starting user level upgrade/downgrade task")

	// First execute data aggregation task
	if err := t.aggregateTransactionData(); err != nil {
		global.GVA_LOG.Error("Data aggregation failed", zap.Error(err))
		return
	}

	// Get all users
	var users []model.User
	err := global.GVA_DB.Preload("AgentLevel").Find(&users).Error
	if err != nil {
		global.GVA_LOG.Error("Failed to get user list", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Found users to process", zap.Int("user_count", len(users)))

	processedCount := 0
	upgradedCount := 0
	downgradedCount := 0
	warningSentCount := 0
	errorCount := 0

	for _, user := range users {
		upgraded, downgraded, warningSent, err := t.processUserLevelChange(user)
		if err != nil {
			global.GVA_LOG.Error("Failed to process user level change",
				zap.String("user_id", user.ID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
			if upgraded {
				upgradedCount++
			}
			if downgraded {
				downgradedCount++
			}
			if warningSent {
				warningSentCount++
			}
		}
	}

	global.GVA_LOG.Info("User level upgrade/downgrade task completed",
		zap.Int("total_users", len(users)),
		zap.Int("processed_count", processedCount),
		zap.Int("upgraded_count", upgradedCount),
		zap.Int("downgraded_count", downgradedCount),
		zap.Int("warning_sent_count", warningSentCount),
		zap.Int("error_count", errorCount))
}

// processUserLevelChange processes level upgrade/downgrade for a single user
func (t *LevelUpgradeTask) processUserLevelChange(user model.User) (bool, bool, bool, error) {
	// Calculate transaction volume for the past 30 days (excluding today)
	yesterday := time.Now().UTC().AddDate(0, 0, -1)
	thirtyDaysAgo := yesterday.AddDate(0, 0, -29) // 30 days ago (including yesterday)

	// Calculate user's 30-day MEME and contract transaction volumes
	memeVolume, contractVolume, err := t.calculateUserVolume(user.ID, thirtyDaysAgo, yesterday)
	if err != nil {
		return false, false, false, err
	}

	// Get all levels
	var levels []model.AgentLevel
	err = global.GVA_DB.Order("id ASC").Find(&levels).Error
	if err != nil {
		return false, false, false, err
	}

	// Determine target level based on MEME and contract volumes
	targetLevel := t.determineTargetLevel(memeVolume, contractVolume, levels)

	// Handle upgrade logic
	if targetLevel.ID > user.AgentLevelID {
		err = t.upgradeUserLevel(user.ID, targetLevel.ID)
		if err != nil {
			return false, false, false, err
		}
		global.GVA_LOG.Info("User level upgrade successful",
			zap.String("user_id", user.ID.String()),
			zap.Uint("old_level", user.AgentLevelID),
			zap.Uint("new_level", targetLevel.ID),
			zap.String("meme_volume", memeVolume.String()),
			zap.String("contract_volume", contractVolume.String()))
		return true, false, false, nil
	}

	// Check if downgrade mechanism is enabled in configuration
	if !global.GVA_CONFIG.System.EnableLevelDowngrade {
		// global.GVA_LOG.Debug("Level downgrade mechanism is disabled in configuration",
		// 	zap.String("user_id", user.ID.String()),
		// 	zap.Uint("level", user.AgentLevelID))
		return false, false, false, nil
	}

	// Check if user meets current level requirements
	meetsCurrentLevel := t.meetsLevelRequirements(memeVolume, contractVolume, user.AgentLevel)

	// Handle downgrade logic with 3-phase grace period
	if !meetsCurrentLevel {
		return t.handleDowngradeLogic(user, memeVolume, contractVolume, levels)
	} else {
		// User meets current level requirements, reset grace period if it was active
		if user.LevelGracePeriodStartedAt != nil {
			err = t.resetGracePeriod(user.ID)
			if err != nil {
				return false, false, false, err
			}
			global.GVA_LOG.Info("Grace period reset - user meets level requirements",
				zap.String("user_id", user.ID.String()),
				zap.Uint("level", user.AgentLevelID),
				zap.String("meme_volume", memeVolume.String()),
				zap.String("contract_volume", contractVolume.String()))
		}
		return false, false, false, nil
	}
}

// handleDowngradeLogic handles the 3-phase 60-day grace period downgrade logic
func (t *LevelUpgradeTask) handleDowngradeLogic(user model.User, memeVolume, contractVolume decimal.Decimal, levels []model.AgentLevel) (bool, bool, bool, error) {
	now := time.Now().UTC()

	// If grace period hasn't started yet, start it (Phase 1: Observation period)
	if user.LevelGracePeriodStartedAt == nil {
		err := t.startGracePeriod(user.ID, &now)
		if err != nil {
			return false, false, false, err
		}
		global.GVA_LOG.Info("Phase 1 started - observation period, user below level requirements",
			zap.String("user_id", user.ID.String()),
			zap.Uint("level", user.AgentLevelID),
			zap.String("meme_volume", memeVolume.String()),
			zap.String("contract_volume", contractVolume.String()))
		return false, false, false, nil
	}

	// Calculate days since grace period started
	daysSinceStart := int(now.Sub(*user.LevelGracePeriodStartedAt).Hours() / 24)

	// Phase 1: Observation period (Days 1-30)
	if daysSinceStart <= 30 {
		global.GVA_LOG.Debug("Phase 1 - observation period, no action taken",
			zap.String("user_id", user.ID.String()),
			zap.Uint("level", user.AgentLevelID),
			zap.Int("days_since_start", daysSinceStart))
		return false, false, false, nil
	}

	// Phase 2: Warning period (Days 31-60)
	if daysSinceStart > 30 && daysSinceStart <= 60 {
		// Send warning notification on day 45 (or first check after day 45)
		if daysSinceStart >= 45 {
			warningSent, err := t.sendWarningNotification(user, daysSinceStart)
			if err != nil {
				global.GVA_LOG.Error("Failed to send warning notification",
					zap.String("user_id", user.ID.String()),
					zap.Error(err))
			} else if warningSent {
				global.GVA_LOG.Info("Phase 2 - warning notification sent",
					zap.String("user_id", user.ID.String()),
					zap.Uint("level", user.AgentLevelID),
					zap.Int("days_since_start", daysSinceStart))
				return false, false, true, nil
			}
		}
		return false, false, false, nil
	}

	// Phase 3: Downgrade execution (Day 61+)
	if daysSinceStart > 60 {
		// Execute downgrade
		targetLevel := t.determineTargetLevel(memeVolume, contractVolume, levels)
		if targetLevel.ID < user.AgentLevelID {
			err := t.downgradeUserLevel(user.ID, targetLevel.ID)
			if err != nil {
				return false, false, false, err
			}
			global.GVA_LOG.Info("Phase 3 - downgrade executed",
				zap.String("user_id", user.ID.String()),
				zap.Uint("old_level", user.AgentLevelID),
				zap.Uint("new_level", targetLevel.ID),
				zap.String("meme_volume", memeVolume.String()),
				zap.String("contract_volume", contractVolume.String()),
				zap.Int("days_since_start", daysSinceStart),
				zap.Time("grace_period_started", *user.LevelGracePeriodStartedAt))

			// Send downgrade notification
			err = t.sendDowngradeNotification(user, targetLevel)
			if err != nil {
				global.GVA_LOG.Error("Failed to send downgrade notification",
					zap.String("user_id", user.ID.String()),
					zap.Error(err))
			}

			return false, true, false, nil
		}
	}

	return false, false, false, nil
}

// sendWarningNotification sends warning notification to user
func (t *LevelUpgradeTask) sendWarningNotification(user model.User, daysSinceStart int) (bool, error) {
	// Check if warning has already been sent to avoid duplicate notifications
	// This could be implemented by adding a field to track warning status
	// For now, we'll implement a simple logic to send warning on day 45

	if daysSinceStart == 45 {
		// TODO: Implement actual notification sending logic
		// This could be:
		// 1. Database notification record
		// 2. Email notification
		// 3. In-app notification
		// 4. Push notification

		global.GVA_LOG.Info("Warning notification would be sent to user",
			zap.String("user_id", user.ID.String()),
			zap.Uint("current_level", user.AgentLevelID),
			zap.String("message", "Your level may be downgraded on day 60, please maintain trading activity"))

		return true, nil
	}

	return false, nil
}

// sendDowngradeNotification sends downgrade notification to user
func (t *LevelUpgradeTask) sendDowngradeNotification(user model.User, newLevel model.AgentLevel) error {
	// TODO: Implement actual notification sending logic
	// This could be:
	// 1. Database notification record
	// 2. Email notification
	// 3. In-app notification
	// 4. Push notification

	global.GVA_LOG.Info("Downgrade notification would be sent to user",
		zap.String("user_id", user.ID.String()),
		zap.Uint("old_level", user.AgentLevelID),
		zap.Uint("new_level", newLevel.ID),
		zap.String("message", "Your level has been downgraded, please check the new benefits"))

	return nil
}

// meetsLevelRequirements checks if user's MEME and contract volumes meet current level requirements
func (t *LevelUpgradeTask) meetsLevelRequirements(memeVolume, contractVolume decimal.Decimal, currentLevel model.AgentLevel) bool {
	// Check if user meets either MEME volume threshold OR contract volume threshold
	memeVolumeMet := memeVolume.GreaterThanOrEqual(currentLevel.MemeVolumeThreshold)
	contractVolumeMet := contractVolume.GreaterThanOrEqual(currentLevel.ContractVolumeThreshold)

	return memeVolumeMet || contractVolumeMet
}

// startGracePeriod starts the 60-day grace period for a user
func (t *LevelUpgradeTask) startGracePeriod(userID uuid.UUID, startTime *time.Time) error {
	return global.GVA_DB.Model(&model.User{}).
		Where("id = ?", userID).
		Update("level_grace_period_started_at", startTime).Error
}

// resetGracePeriod resets the grace period (clears the timer)
func (t *LevelUpgradeTask) resetGracePeriod(userID uuid.UUID) error {
	return global.GVA_DB.Model(&model.User{}).
		Where("id = ?", userID).
		Update("level_grace_period_started_at", nil).Error
}

// downgradeUserLevel downgrades user level and clears grace period
func (t *LevelUpgradeTask) downgradeUserLevel(userID uuid.UUID, newLevelID uint) error {
	now := time.Now().UTC()

	return global.GVA_DB.Model(&model.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"agent_level_id":                newLevelID,
			"level_upgraded_at":             &now,
			"level_grace_period_started_at": nil, // Clear grace period after downgrade
		}).Error
}

// calculateUserVolume calculates user's MEME and contract transaction volumes within specified time period
func (t *LevelUpgradeTask) calculateUserVolume(userID uuid.UUID, startDate, endDate time.Time) (decimal.Decimal, decimal.Decimal, error) {
	// Calculate MEME transaction volume from daily_meme_volumes table
	var memeVolume decimal.Decimal
	err := global.GVA_DB.Model(&model.DailyMemeVolume{}).
		Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate).
		Select("COALESCE(SUM(meme_volume_usd), 0)").
		Scan(&memeVolume).Error

	if err != nil {
		return decimal.Zero, decimal.Zero, err
	}

	// Calculate contract transaction volume from daily_user_volumes table
	var contractVolume decimal.Decimal
	err = global.GVA_DB.Model(&model.DailyUserVolume{}).
		Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate).
		Select("COALESCE(SUM(contract_volume_usd), 0)").
		Scan(&contractVolume).Error

	if err != nil {
		return decimal.Zero, decimal.Zero, err
	}

	return memeVolume, contractVolume, nil
}

// determineTargetLevel determines target level based on MEME and contract volumes
func (t *LevelUpgradeTask) determineTargetLevel(memeVolume, contractVolume decimal.Decimal, levels []model.AgentLevel) model.AgentLevel {
	// Default to lowest level
	targetLevel := levels[0]

	// Iterate from highest to lowest level, find first level that meets requirements
	for i := len(levels) - 1; i >= 1; i-- {
		level := levels[i]
		// Check if either MEME or contract volume meets this level's requirements
		if memeVolume.GreaterThanOrEqual(level.MemeVolumeThreshold) ||
			contractVolume.GreaterThanOrEqual(level.ContractVolumeThreshold) {
			targetLevel = level
			break
		}
	}

	return targetLevel
}

// upgradeUserLevel upgrades user level
func (t *LevelUpgradeTask) upgradeUserLevel(userID uuid.UUID, newLevelID uint) error {
	now := time.Now().UTC()

	return global.GVA_DB.Model(&model.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"agent_level_id":                newLevelID,
			"level_upgraded_at":             &now,
			"level_grace_period_started_at": nil, // Clear grace period
		}).Error
}

// aggregateTransactionData aggregates ActivityCashback, HyperLiquidTransaction and CommissionLedger data to DailyMemeVolume and DailyUserVolume
func (t *LevelUpgradeTask) aggregateTransactionData() error {
	global.GVA_LOG.Info("Starting transaction data aggregation task")

	// Get yesterday's date (UTC)
	yesterday := time.Now().UTC().AddDate(0, 0, -1)
	yesterdayDate := yesterday.Format("2006-01-02")

	// Aggregate all transaction data to DailyMemeVolume and DailyUserVolume
	if err := t.aggregateAllTransactions(yesterday); err != nil {
		return err
	}

	// Update accumulated trading volume in user_tier_info for Activity Cashback
	if err := t.updateAccumulatedTradingVolumes(); err != nil {
		global.GVA_LOG.Error("Failed to update accumulated trading volumes", zap.Error(err))
		// Don't fail the entire process if this fails
	}

	global.GVA_LOG.Info("Transaction data aggregation task completed", zap.String("date", yesterdayDate))
	return nil
}

// aggregateAllTransactions aggregates all transaction data to DailyUserVolume and DailyMemeVolume
func (t *LevelUpgradeTask) aggregateAllTransactions(date time.Time) error {
	global.GVA_LOG.Info("Starting to aggregate all transaction data", zap.Time("date", date))
	// Query yesterday's ActivityCashback data (MEME transactions), grouped by user
	var memeResults []struct {
		UserID           uuid.UUID       `json:"user_id"`
		TotalVolume      decimal.Decimal `json:"total_volume"`
		TransactionCount int64           `json:"transaction_count"`
	}

	dateStr := date.Format("2006-01-02")

	var latestSolPrice decimal.Decimal
	err := global.GVA_DB.Model(&model.SolPriceSnapshot{}).
		Select("price").
		Order("timestamp DESC").
		Limit(1).
		Scan(&latestSolPrice).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get latest SOL price, using zero for MEME volume calculation",
			zap.String("date", dateStr),
			zap.Error(err))
		latestSolPrice = decimal.Zero
	}

	err = global.GVA_DB.Model(&model.AffiliateTransaction{}).
		Select(`
			user_id,
			COALESCE(SUM(quote_amount * ?), 0) as total_volume,
			COUNT(*) as transaction_count
		`, latestSolPrice.InexactFloat64()).
		Where("DATE(created_at) = ?", dateStr).
		Where("status = ?", "Completed").
		Where("user_id IS NOT NULL").
		Group("user_id").
		Scan(&memeResults).Error

	if err != nil {
		global.GVA_LOG.Error("Failed to query AffiliateTransaction for MEME volume", zap.Error(err))
		memeResults = []struct {
			UserID           uuid.UUID       `json:"user_id"`
			TotalVolume      decimal.Decimal `json:"total_volume"`
			TransactionCount int64           `json:"transaction_count"`
		}{}
	}

	global.GVA_LOG.Info("Found AffiliateTransaction MEME data", zap.Int("user_count", len(memeResults)))

	// Query yesterday's HyperLiquidTransaction data (contract transactions), grouped by user
	var contractResults []struct {
		UserID           uuid.UUID       `json:"user_id"`
		TotalVolume      decimal.Decimal `json:"total_volume"`
		TransactionCount int64           `json:"transaction_count"`
	}
	// Not only should we calculate the transaction volume of avg_price * total_sz in the HyperLiquidTransaction table,
	// but we should also calculate the user's commission transaction volume in the CommissionLedger table.
	err = global.GVA_DB.Model(&model.HyperLiquidTransaction{}).
		Select(`
			user_id,
			COALESCE(SUM(avg_price * NULLIF(total_sz, '')::numeric), 0) as total_volume,
			COUNT(*) as transaction_count
		`).
		Where("DATE(SUBSTRING(created_at, 1, 19)::timestamp) = ?::date", dateStr).
		Where("status = ?", "filled").
		Where("user_id IS NOT NULL").
		Group("user_id").
		Scan(&contractResults).Error

	if err != nil {
		return err
	}

	global.GVA_LOG.Info("Found HyperLiquidTransaction data", zap.Int("user_count", len(contractResults)))

	// Query the user's referral relationships to obtain direct, indirect, and extended user transaction volumes.
	// Calculate the total transaction volume in each user's referral network.
	var referralVolumeResults []struct {
		ReferrerID       uuid.UUID       `json:"referrer_id"`
		TotalVolume      decimal.Decimal `json:"total_volume"`
		TransactionCount int64           `json:"transaction_count"`
	}

	// Query the transaction volume of the referral network: current user + directly referred users + indirectly referred users + extended referred users (depth <= 3)
	// This query calculates the total transaction volume of all users in the referral network for each referrer.
	err = global.GVA_DB.Model(&model.HyperLiquidTransaction{}).
		Joins("JOIN referrals r ON hyper_liquid_transactions.user_id = r.user_id").
		Select(`
			r.referrer_id as referrer_id,
			COALESCE(SUM(hyper_liquid_transactions.avg_price * NULLIF(hyper_liquid_transactions.total_sz, '')::numeric), 0) as total_volume,
			COUNT(*) as transaction_count
		`).
		Where("DATE(SUBSTRING(hyper_liquid_transactions.created_at, 1, 19)::timestamp) = ?::date", dateStr).
		Where("hyper_liquid_transactions.status = ?", "filled").
		Where("r.depth <= 3").
		Where("r.referrer_id IS NOT NULL").
		Group("r.referrer_id").
		Scan(&referralVolumeResults).Error

	if err != nil {
		return err
	}

	global.GVA_LOG.Info("Found referral volume data", zap.Int("user_count", len(referralVolumeResults)))

	// Process MEME transaction data - aggregate to DailyMemeVolume and add points
	for _, result := range memeResults {
		// Update daily meme volume
		err = t.upsertDailyMemeVolume(result.UserID, date, result.TotalVolume)
		if err != nil {
			global.GVA_LOG.Error("Failed to update DailyMemeVolume",
				zap.String("user_id", result.UserID.String()),
				zap.Time("date", date),
				zap.Error(err))
			continue
		}

		// Add points to user based on daily volume USD
		if err := t.addPointsForUser(result.UserID, result.TotalVolume); err != nil {
			global.GVA_LOG.Error("Failed to add points for user",
				zap.String("user_id", result.UserID.String()),
				zap.String("volume_usd", result.TotalVolume.String()),
				zap.Time("date", date),
				zap.Error(err))
			// Don't fail the aggregation if points addition fails
		}
	}

	// Process contract transaction data - aggregate to DailyUserVolume
	// Create a map to combine HyperLiquidTransaction and referral volume data
	contractVolumeMap := make(map[uuid.UUID]decimal.Decimal)

	// Add HyperLiquidTransaction volumes (当前用户的交易量)
	for _, result := range contractResults {
		contractVolumeMap[result.UserID] = contractVolumeMap[result.UserID].Add(result.TotalVolume)
	}

	// Add referral network volumes (推荐网络中的交易量：直接+间接+延长)
	for _, result := range referralVolumeResults {
		contractVolumeMap[result.ReferrerID] = contractVolumeMap[result.ReferrerID].Add(result.TotalVolume)
	}

	// Process combined contract volume data
	for userID, totalVolume := range contractVolumeMap {
		err = t.upsertDailyUserVolumeContract(userID, date, totalVolume)
		if err != nil {
			global.GVA_LOG.Error("Failed to update DailyUserVolume contract volume",
				zap.String("user_id", userID.String()),
				zap.Time("date", date),
				zap.Error(err))
			continue
		}
	}

	global.GVA_LOG.Info("Transaction data aggregation completed",
		zap.Int("meme_users", len(memeResults)),
		zap.Int("contract_users", len(contractResults)),
		zap.Int("referral_volume_users", len(referralVolumeResults)),
		zap.Int("total_contract_users", len(contractVolumeMap)))

	return nil
}

// upsertDailyMemeVolume updates or inserts DailyMemeVolume record
func (t *LevelUpgradeTask) upsertDailyMemeVolume(userID uuid.UUID, date time.Time, memeVolume decimal.Decimal) error {
	// Format date as YYYY-MM-DD
	dateStr := date.Format("2006-01-02")

	// Try to find existing record
	var existingRecord model.DailyMemeVolume
	result := global.GVA_DB.Where("user_id = ? AND date = ?", userID, dateStr).First(&existingRecord)

	if result.Error != nil {
		// Record doesn't exist, create new one
		if result.Error.Error() == "record not found" {
			newRecord := model.DailyMemeVolume{
				UserID:        userID,
				Date:          date,
				MemeVolumeUSD: memeVolume,
			}
			return global.GVA_DB.Create(&newRecord).Error
		}
		return result.Error
	}

	// Record exists, update it
	return global.GVA_DB.Model(&existingRecord).Update("meme_volume_usd", memeVolume).Error
}

// upsertDailyUserVolumeContract updates or inserts DailyUserVolume record for contract volume only
func (t *LevelUpgradeTask) upsertDailyUserVolumeContract(userID uuid.UUID, date time.Time, contractVolume decimal.Decimal) error {
	// Format date as YYYY-MM-DD
	dateStr := date.Format("2006-01-02")

	// Try to find existing record
	var existingRecord model.DailyUserVolume
	result := global.GVA_DB.Where("user_id = ? AND date = ?", userID, dateStr).First(&existingRecord)

	if result.Error != nil {
		// Record doesn't exist, create new one
		if result.Error.Error() == "record not found" {
			newRecord := model.DailyUserVolume{
				UserID:            userID,
				Date:              date,
				ContractVolumeUSD: contractVolume,
				MemeVolumeUSD:     decimal.Zero, // Initialize with zero for MEME volume
			}
			return global.GVA_DB.Create(&newRecord).Error
		}
		return result.Error
	}

	// Record exists, update contract volume only
	return global.GVA_DB.Model(&existingRecord).Update("contract_volume_usd", contractVolume).Error
}

// updateAccumulatedTradingVolumes calculates and updates all-time accumulated trading volume for all users in user_tier_info
func (t *LevelUpgradeTask) updateAccumulatedTradingVolumes() error {
	global.GVA_LOG.Info("Starting accumulated trading volume update for Activity Cashback")

	// Get all users who have MEME trading volume data
	var userVolumeResults []struct {
		UserID      uuid.UUID       `json:"user_id"`
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	// Calculate SUM of all-time MEME volume for each user from daily_meme_volumes
	err := global.GVA_DB.Model(&model.DailyMemeVolume{}).
		Select("user_id, COALESCE(SUM(meme_volume_usd), 0) as total_volume").
		Group("user_id").
		Having("COALESCE(SUM(meme_volume_usd), 0) > 0").
		Scan(&userVolumeResults).Error

	if err != nil {
		return fmt.Errorf("failed to calculate accumulated trading volumes: %w", err)
	}

	global.GVA_LOG.Info("Found users with MEME trading volume", zap.Int("user_count", len(userVolumeResults)))

	// Update each user's accumulated trading volume in user_tier_info
	updatedCount := 0
	for _, result := range userVolumeResults {
		if err := t.updateUserTierInfoTradingVolume(result.UserID, result.TotalVolume); err != nil {
			global.GVA_LOG.Error("Failed to update user tier info trading volume",
				zap.String("user_id", result.UserID.String()),
				zap.String("total_volume", result.TotalVolume.String()),
				zap.Error(err))
			continue
		}
		updatedCount++
	}

	global.GVA_LOG.Info("Accumulated trading volume update completed",
		zap.Int("total_users", len(userVolumeResults)),
		zap.Int("updated_users", updatedCount))

	return nil
}

// updateUserTierInfoTradingVolume updates or creates user_tier_info record with accumulated trading volume
func (t *LevelUpgradeTask) updateUserTierInfoTradingVolume(userID uuid.UUID, totalVolume decimal.Decimal) error {
	// Try to find existing user_tier_info record
	var userTierInfo model.UserTierInfo
	result := global.GVA_DB.Where("user_id = ?", userID).First(&userTierInfo)

	if result.Error != nil {
		// Record doesn't exist, create new one with default values
		if result.Error.Error() == "record not found" {
			newRecord := model.UserTierInfo{
				UserID:                userID,
				CurrentTier:           1, // Default tier
				TotalPoints:           0,
				PointsThisMonth:       0,
				TradingVolumeUSD:      totalVolume,
				ActiveDaysThisMonth:   0,
				CumulativeCashbackUSD: decimal.Zero,
				ClaimableCashbackUSD:  decimal.Zero,
				ClaimedCashbackUSD:    decimal.Zero,
				MonthlyResetAt:        nil,
				CreatedAt:             time.Now(),
				UpdatedAt:             time.Now(),
			}
			return global.GVA_DB.Create(&newRecord).Error
		}
		return result.Error
	}

	// Record exists, update only the trading volume
	return global.GVA_DB.Model(&userTierInfo).Update("trading_volume_usd", totalVolume).Error
}

// addPointsForUser calculates and adds points to user based on daily trading volume USD
func (t *LevelUpgradeTask) addPointsForUser(userID uuid.UUID, volumeUSD decimal.Decimal) error {
	// Convert volume to float64 for comparison
	volume, _ := volumeUSD.Float64()

	// Calculate points based on volume tiers (same as activity cashback system)
	var points int
	switch {
	case volume >= 10000:
		points = 40
	case volume >= 3000:
		points = 25
	case volume >= 500:
		points = 12
	case volume >= 100:
		points = 5
	case volume >= 1:
		points = 1
	default:
		points = 0
	}

	if points <= 0 {
		return nil // No points to add
	}

	// Get or create user tier info
	var userTierInfo model.UserTierInfo
	err := global.GVA_DB.Where("user_id = ?", userID).First(&userTierInfo).Error
	if err != nil {
		if err.Error() == "record not found" {
			// Create new user tier info
			now := time.Now()
			userTierInfo = model.UserTierInfo{
				UserID:                userID,
				CurrentTier:           1,
				TotalPoints:           0,
				PointsThisMonth:       0,
				TradingVolumeUSD:      decimal.Zero,
				ActiveDaysThisMonth:   0,
				CumulativeCashbackUSD: decimal.Zero,
				ClaimableCashbackUSD:  decimal.Zero,
				ClaimedCashbackUSD:    decimal.Zero,
				LastActivityDate:      &now,
				CreatedAt:             now,
				UpdatedAt:             now,
			}
		} else {
			return fmt.Errorf("failed to get user tier info: %w", err)
		}
	}

	// Add points using the model method
	userTierInfo.AddPoints(points)

	// Save or update user tier info
	if userTierInfo.UserID == uuid.Nil {
		// This shouldn't happen, but just in case
		userTierInfo.UserID = userID
		err = global.GVA_DB.Create(&userTierInfo).Error
	} else {
		err = global.GVA_DB.Save(&userTierInfo).Error
	}

	if err != nil {
		return fmt.Errorf("failed to save user tier info: %w", err)
	}

	global.GVA_LOG.Info("Points added to user from daily volume aggregation",
		zap.String("user_id", userID.String()),
		zap.Float64("volume_usd", volume),
		zap.Int("points_added", points),
		zap.Int("total_points", userTierInfo.TotalPoints))

	return nil
}
