package resolvers

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/invitation"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/reward"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

type RewardResolver struct {
	rewardService           *reward.RewardDataService
	rewardClaimService      *reward.RewardClaimResultService
	invitationRecordService *invitation.InvitationRecordService
	logger                  *zap.Logger
}

func NewRewardResolver() *RewardResolver {
	return &RewardResolver{
		rewardService:           reward.NewRewardDataService(),
		rewardClaimService:      reward.NewRewardClaimResultService(),
		invitationRecordService: invitation.NewInvitationRecordService(),
		logger:                  global.GVA_LOG,
	}
}

func (r *RewardResolver) InvitationRecords(ctx context.Context, input gql_model.InvitationRecordRequest) (*gql_model.InvitationRecordResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.InvitationRecordResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	request := &invitation.InvitationRecordRequest{
		Page:     input.Page,
		PageSize: input.PageSize,
	}

	invitationRecords, err := r.invitationRecordService.GetInvitationRecords(ctx, userID, request)
	if err != nil {
		return &gql_model.InvitationRecordResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to obtain invitation record: %v", err),
		}, nil
	}

	var gqlRecords []*gql_model.InvitationRecord
	for _, record := range invitationRecords.Data {
		gqlRecord := &gql_model.InvitationRecord{
			Address:           record.Address,
			TransactionVolume: record.TransactionVolume,
			//InvitedWithdrawal: record.InvitedWithdrawal,
			InvitedWithdrawal: decimal.NewFromFloat(record.InvitedWithdrawal).String(),
			Date:              record.Date,
			ChainID:           record.ChainID,
			Token:             record.Token,
		}
		gqlRecords = append(gqlRecords, gqlRecord)
	}

	return &gql_model.InvitationRecordResponse{
		Success:  true,
		Message:  "Get invitation record successfully",
		Data:     gqlRecords,
		Total:    invitationRecords.Total,
		Page:     invitationRecords.Page,
		PageSize: invitationRecords.PageSize,
	}, nil
}

func (r *RewardResolver) WithdrawalRecords(ctx context.Context, input gql_model.WithdrawalRecordRequest) (*gql_model.WithdrawalRecordResponse, error) {

	// Create repository instance
	activityCashbackRepo := repo.NewActivityCashbackRepository()

	// Get current user ID (from authentication info in context)
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		message := "Failed to get user information"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     input.Page,
			PageSize: input.PageSize,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Query claimed activity cashback records with pagination
	claimedCashbacks, err := activityCashbackRepo.GetClaimedCashbacksByUserIDWithPagination(ctx, userID, input.Page, input.PageSize)
	if err != nil {
		message := "Failed to query activity cashback records"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     input.Page,
			PageSize: input.PageSize,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Query claimed commission records with pagination
	claimedCommissions, totalCommissions, err := r.getClaimedCommissionsByUserIDWithPagination(ctx, userID, input.Page, input.PageSize)
	if err != nil {
		message := "Failed to query commission records"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     input.Page,
			PageSize: input.PageSize,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Convert record format
	var withdrawalRecords []*gql_model.WithdrawalRecord

	// Convert activity cashback records
	for _, cashback := range claimedCashbacks.Data {
		// For meme: query AffiliateTransaction.TxHash, associated with ActivityCashback.CashbackAmountUSD, status CLAIMED
		// Generate hash value (using ID as unique identifier)
		hash := fmt.Sprintf("01x35...%s", cashback.ID.String()[:8])

		// Format reward amount (including currency symbol)
		var withdrawalReward string
		if cashback.CashbackAmountSOL.GreaterThan(decimal.Zero) {
			withdrawalReward = cashback.CashbackAmountSOL.String()
		} else {
			withdrawalReward = cashback.CashbackAmountUSD.String()
		}

		// Format date
		var date string
		if cashback.ClaimedAt != nil {
			date = cashback.ClaimedAt.Format("01-02")
		} else {
			date = "N/A"
		}

		withdrawalRecords = append(withdrawalRecords, &gql_model.WithdrawalRecord{
			Hash:             hash,
			WithdrawalReward: withdrawalReward,
			Date:             date,
		})
	}

	for _, commission := range claimedCommissions {
		// For contract: query HyperLiquidTransaction.Hash, associated with CommissionLedger.CommissionAmount, status CLAIMED
		// TODO: Process commission record
		hash := fmt.Sprintf("01x35...%s", commission.ID.String()[:8])

		withdrawalReward := commission.CommissionAmount.String()

		var date string
		if commission.ClaimedAt != nil {
			date = commission.ClaimedAt.Format("01-02")
		} else {
			date = "N/A"
		}

		withdrawalRecords = append(withdrawalRecords, &gql_model.WithdrawalRecord{
			Hash:             hash,
			WithdrawalReward: withdrawalReward,
			Date:             date,
		})
	}

	sort.Slice(withdrawalRecords, func(i, j int) bool {
		return withdrawalRecords[i].Date > withdrawalRecords[j].Date
	})

	// Calculate total records (cashbacks + commissions)
	totalRecords := int(claimedCashbacks.Total + totalCommissions)

	message := "Query successful"
	return &gql_model.WithdrawalRecordResponse{
		Data:     withdrawalRecords,
		Total:    totalRecords,
		Page:     input.Page,
		PageSize: input.PageSize,
		Success:  true,
		Message:  &message,
	}, nil
}

// getClaimedCommissionsByUserID Query user's claimed commission records
func (r *RewardResolver) getClaimedCommissionsByUserID(ctx context.Context, userID uuid.UUID) ([]model.CommissionLedger, error) {
	// Directly query database to get claimed commission records
	var commissions []model.CommissionLedger
	err := global.GVA_DB.WithContext(ctx).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Preload("RecipientUser").
		Find(&commissions).Error

	if err != nil {
		return nil, fmt.Errorf("Failed to query commission records: %w", err)
	}

	return commissions, nil
}

// getClaimedCommissionsByUserIDWithPagination Query user's claimed commission records with pagination
func (r *RewardResolver) getClaimedCommissionsByUserIDWithPagination(ctx context.Context, userID uuid.UUID, page, pageSize int) ([]model.CommissionLedger, int64, error) {
	var commissions []model.CommissionLedger
	var total int64

	// Calculate offset
	offset := (page - 1) * pageSize

	// Get total count
	err := global.GVA_DB.WithContext(ctx).Debug().
		Model(&model.CommissionLedger{}).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("Failed to query commission records count: %w", err)
	}

	// Get paginated data
	err = global.GVA_DB.WithContext(ctx).Debug().
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Preload("RecipientUser").
		Order("claimed_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&commissions).Error

	if err != nil {
		return nil, 0, fmt.Errorf("Failed to query commission records: %w", err)
	}

	return commissions, total, nil
}

// RewardClaimHistory handles the reward claim history query
func (r *RewardResolver) RewardClaimHistory(ctx context.Context, input gql_model.RewardClaimHistoryRequest) (*gql_model.RewardClaimHistoryResponse, error) {
	// Get user ID from context
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	// Calculate offset for pagination
	offset := (input.Page - 1) * input.PageSize

	// Convert GraphQL enum to model enum if result filter is provided
	var resultFilter *model.RewardClaimResult
	if input.Result != nil {
		switch *input.Result {
		case gql_model.RewardClaimHistoryResultEnumPending:
			result := model.RewardClaimResultPending
			resultFilter = &result
		case gql_model.RewardClaimHistoryResultEnumProcessing:
			result := model.RewardClaimResultProcessing
			resultFilter = &result
		case gql_model.RewardClaimHistoryResultEnumSuccess:
			result := model.RewardClaimResultSuccess
			resultFilter = &result
		case gql_model.RewardClaimHistoryResultEnumFailed:
			result := model.RewardClaimResultFailed
			resultFilter = &result
		}
	}

	// Convert GraphQL string to model enum if type filter is provided
	isCashback := false
	if input.Type != nil {
		switch *input.Type {
		case "meme_cashback":
			isCashback = true
		case "meme_commission":
			isCashback = false
		}
	}

	if input.IsCashback != nil {
		isCashback = *input.IsCashback
	}

	// Get paginated reward claim results using service with optional filters
	records, totalCount, err := r.rewardClaimService.GetUserClaimHistory(userID, input.PageSize, offset, isCashback, resultFilter)
	if err != nil {
		r.logger.Error("Failed to get reward claim history", zap.Error(err))
		return nil, fmt.Errorf("Failed to get reward claim history")
	}

	// Convert model records to GraphQL format
	var gqlRecords []*gql_model.RewardClaimHistoryRecord
	for _, record := range records {
		gqlRecord := &gql_model.RewardClaimHistoryRecord{
			ID:        record.ID.String(),
			UserID:    record.UserID.String(),
			Address:   record.Address,
			Amount:    record.Amount.String(),
			AmountUsd: record.AmountUsd.String(),
			Token:     record.Token,
			ChainID:   record.ChainID,
			Type:      string(record.Type),
			Result:    string(record.Result),
			CreatedAt: record.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UpdatedAt: record.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		}

		// Handle optional fields
		if record.TransactionHash != nil {
			gqlRecord.TransactionHash = record.TransactionHash
		}
		if record.OnchainTimestamp != nil {
			timestamp := time.Unix(*record.OnchainTimestamp, 0).Format("2006-01-02T15:04:05Z07:00")
			gqlRecord.OnchainTimestamp = &timestamp
		}
		if record.ErrorMessage != nil {
			gqlRecord.ErrorMessage = record.ErrorMessage
		}
		if record.ErrorCode != nil {
			gqlRecord.ErrorCode = record.ErrorCode
		}
		if record.ProcessedAt != nil {
			processedAt := record.ProcessedAt.Format("2006-01-02T15:04:05Z07:00")
			gqlRecord.ProcessedAt = &processedAt
		}

		gqlRecords = append(gqlRecords, gqlRecord)
	}

	return &gql_model.RewardClaimHistoryResponse{
		Data:     gqlRecords,
		Total:    int(totalCount),
		Page:     input.Page,
		PageSize: input.PageSize,
		Success:  true,
	}, nil
}
