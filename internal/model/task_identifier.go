package model

import "fmt"

// TaskIdentifier represents unique identifiers for tasks
type TaskIdentifier string

// Daily Task Identifiers
const (
	TaskIDDailyCheckin           TaskIdentifier = "DAILY_CHECKIN"
	TaskIDMemeTradeDaily         TaskIdentifier = "MEME_TRADE_DAILY"
	TaskIDPerpetualTradeDaily    TaskIdentifier = "PERPETUAL_TRADE_DAILY"
	TaskIDMarketPageView         TaskIdentifier = "MARKET_PAGE_VIEW"
	TaskIDCheckMarketTrends      TaskIdentifier = "CHECK_MARKET_TRENDS"
	TaskIDConsecutiveCheckin3    TaskIdentifier = "CONSECUTIVE_CHECKIN_3"
	TaskIDConsecutiveCheckin7    TaskIdentifier = "CONSECUTIVE_CHECKIN_7"
	TaskIDConsecutiveCheckin30   TaskIdentifier = "CONSECUTIVE_CHECKIN_30"
	TaskIDConsecutiveTradingDays TaskIdentifier = "CONSECUTIVE_TRADING_DAYS"
)

// Community Task Identifiers
const (
	TaskIDTwitterFollow      TaskIdentifier = "TWITTER_FOLLOW"
	TaskIDTwitterRetweet     TaskIdentifier = "TWITTER_RETWEET"
	TaskIDTwitterLike        TaskIdentifier = "TWITTER_LIKE"
	TaskIDTelegramJoin       TaskIdentifier = "TELEGRAM_JOIN"
	TaskIDInviteFriends      TaskIdentifier = "INVITE_FRIENDS"
	TaskIDShareReferral      TaskIdentifier = "SHARE_REFERRAL"
	TaskIDShareEarningsChart TaskIdentifier = "SHARE_EARNINGS_CHART"
)

// CommunityTasksRequiring2MinuteWait defines which community tasks require 2-minute wait
//
// To add a new task that requires 2-minute wait:
// 1. Add the TaskIdentifier constant above
// 2. Add it to this slice
// 3. Add the corresponding case in task_processors.go ProcessTask method
// 4. Update the unit tests in task_identifier_test.go
var CommunityTasksRequiring2MinuteWait = []TaskIdentifier{
	TaskIDTwitterFollow,
	TaskIDTwitterRetweet,
	TaskIDTwitterLike,
	TaskIDTelegramJoin,
}

// RequiresTwoMinuteWait checks if a task identifier requires 2-minute wait
func RequiresTwoMinuteWait(taskID TaskIdentifier) bool {
	for _, id := range CommunityTasksRequiring2MinuteWait {
		if id == taskID {
			return true
		}
	}
	return false
}

// Trading Task Identifiers
const (
	TaskIDTradingPoints          TaskIdentifier = "TRADING_POINTS"
	TaskIDAccumulatedTrading10K  TaskIdentifier = "ACCUMULATED_TRADING_10K"
	TaskIDAccumulatedTrading50K  TaskIdentifier = "ACCUMULATED_TRADING_50K"
	TaskIDAccumulatedTrading100K TaskIdentifier = "ACCUMULATED_TRADING_100K"
	TaskIDAccumulatedTrading500K TaskIdentifier = "ACCUMULATED_TRADING_500K"
)

// TaskCategoryName defines task category names as enum for type safety
type TaskCategoryName string

const (
	CategoryDaily     TaskCategoryName = "daily"
	CategoryCommunity TaskCategoryName = "community"
	CategoryTrading   TaskCategoryName = "trading"
)

// String returns the string representation of TaskCategoryName
func (t TaskCategoryName) String() string {
	return string(t)
}

// IsValid checks if the category name is valid
func (t TaskCategoryName) IsValid() bool {
	switch t {
	case CategoryDaily, CategoryCommunity, CategoryTrading:
		return true
	default:
		return false
	}
}

// GetDisplayName returns the display name for the category
func (t TaskCategoryName) GetDisplayName() string {
	switch t {
	case CategoryDaily:
		return "Daily Tasks"
	case CategoryCommunity:
		return "Community Tasks"
	case CategoryTrading:
		return "Trading Tasks"
	default:
		return string(t)
	}
}

// GetDescription returns the description for the category
func (t TaskCategoryName) GetDescription() string {
	switch t {
	case CategoryDaily:
		return "Complete these tasks daily to earn points"
	case CategoryCommunity:
		return "Engage with our community to earn rewards"
	case CategoryTrading:
		return "Complete trading activities to earn points"
	default:
		return ""
	}
}

// GetIcon returns the icon for the category
func (t TaskCategoryName) GetIcon() string {
	switch t {
	case CategoryDaily:
		return "calendar"
	case CategoryCommunity:
		return "users"
	case CategoryTrading:
		return "trending-up"
	default:
		return ""
	}
}

// GetSortOrder returns the sort order for the category
func (t TaskCategoryName) GetSortOrder() int {
	switch t {
	case CategoryDaily:
		return 1
	case CategoryCommunity:
		return 2
	case CategoryTrading:
		return 3
	default:
		return 999
	}
}

// AllCategories returns all valid category names
func AllCategories() []TaskCategoryName {
	return []TaskCategoryName{CategoryDaily, CategoryCommunity, CategoryTrading}
}

// TaskCategoryType defines task categories for better organization (deprecated, use TaskCategoryName)
type TaskCategoryType = TaskCategoryName

// TaskDefinition contains all information about a task
type TaskDefinition struct {
	Category    TaskCategoryType `json:"category"`
	DisplayName string           `json:"display_name"`
	Description string           `json:"description"`
	Points      int              `json:"points"`
	MaxDaily    *int             `json:"max_daily,omitempty"` // Max completions per day
	RequiresKYC bool             `json:"requires_kyc"`        // Requires KYC verification
	MinTier     *int             `json:"min_tier,omitempty"`  // Minimum user tier required
}

// TaskDefinitionRegistry contains all task definitions with metadata
var TaskDefinitionRegistry = map[TaskIdentifier]TaskDefinition{
	// Daily Tasks
	TaskIDDailyCheckin: {
		Category:    CategoryDaily,
		DisplayName: "Daily Login",
		Description: "Refresh after UTC 0:00, log in to claim",
		Points:      5,
		MaxDaily:    func() *int { v := 1; return &v }(),
		RequiresKYC: false,
	},
	TaskIDMemeTradeDaily: {
		Category:    CategoryDaily,
		DisplayName: "Complete one meme transaction",
		Description: "Complete one MEME transaction between UTC 0:00-23:59",
		Points:      200,
		MaxDaily:    func() *int { v := 1; return &v }(),
		RequiresKYC: true,
	},
	TaskIDPerpetualTradeDaily: {
		Category:    CategoryDaily,
		DisplayName: "Complete one contract transaction",
		Description: "Complete one contract opening or closing between UTC 0:00-23:59",
		Points:      200,
		MaxDaily:    func() *int { v := 1; return &v }(),
		RequiresKYC: true,
	},
	TaskIDMarketPageView: {
		Category:    CategoryDaily,
		DisplayName: "View market page",
		Description: "View the market page to stay updated",
		Points:      5,
		MaxDaily:    func() *int { v := 1; return &v }(),
		RequiresKYC: false,
	},
	TaskIDCheckMarketTrends: {
		Category:    CategoryDaily,
		DisplayName: "Check Market Trends",
		Description: "Visit the homepage to check market trends",
		Points:      5,
		MaxDaily:    func() *int { v := 1; return &v }(),
		RequiresKYC: false,
	},

	TaskIDConsecutiveCheckin3: {
		Category:    CategoryDaily,
		DisplayName: "Log in continuously for 3 days",
		Description: "Consecutive sign-ins for 3 days to earn 50 points",
		Points:      50,
		RequiresKYC: false,
	},
	TaskIDConsecutiveCheckin7: {
		Category:    CategoryDaily,
		DisplayName: "Log in continuously for 7 days",
		Description: "Consecutive sign-ins for 7 days to earn 200 points",
		Points:      200,
		RequiresKYC: false,
	},
	TaskIDConsecutiveCheckin30: {
		Category:    CategoryDaily,
		DisplayName: "Log in continuously for 30 days",
		Description: "Consecutive sign-ins for 30 days to earn 1000 points",
		Points:      1000,
		RequiresKYC: false,
	},
	TaskIDConsecutiveTradingDays: {
		Category:    CategoryDaily,
		DisplayName: "Consecutive Trading Days",
		Description: "Trade for consecutive days to earn bonus points (3/7/15/30 days)",
		Points:      0, // Variable points based on streak
		RequiresKYC: true,
	},

	// Community Tasks
	TaskIDTwitterFollow: {
		Category:    CategoryCommunity,
		DisplayName: "Follow on Twitter",
		Description: "Points awarded 2 minutes after clicking",
		Points:      50,
		RequiresKYC: false,
	},
	TaskIDTwitterRetweet: {
		Category:    CategoryCommunity,
		DisplayName: "Retweet",
		Description: "Points awarded 2 minutes after clicking",
		Points:      10,
		RequiresKYC: false,
	},
	TaskIDTwitterLike: {
		Category:    CategoryCommunity,
		DisplayName: "Like Tweet",
		Description: "Points awarded 2 minutes after clicking",
		Points:      10,
		RequiresKYC: false,
	},
	TaskIDTelegramJoin: {
		Category:    CategoryCommunity,
		DisplayName: "Join Telegram",
		Description: "Points awarded 2 minutes after clicking",
		Points:      30,
		RequiresKYC: false,
	},
	TaskIDInviteFriends: {
		Category:    CategoryCommunity,
		DisplayName: "Invite Friends",
		Description: "Inviting friends counts as one only if they have trading records on the xbit platform",
		Points:      100,
		RequiresKYC: true,
	},
	TaskIDShareReferral: {
		Category:    CategoryCommunity,
		DisplayName: "Share referral link",
		Description: "Share your referral link on social media",
		Points:      25,
		RequiresKYC: false,
	},
	TaskIDShareEarningsChart: {
		Category:    CategoryCommunity,
		DisplayName: "Share Earnings Chart",
		Description: "Share earnings chart to earn points",
		Points:      10,
		MaxDaily:    func() *int { v := 1; return &v }(),
		RequiresKYC: false,
	},

	// Trading Tasks
	TaskIDTradingPoints: {
		Category:    CategoryTrading,
		DisplayName: "Trading Points",
		Description: "Earn points based on trading volume (1-40 points)",
		Points:      0, // Variable points based on volume
		RequiresKYC: true,
	},
	TaskIDAccumulatedTrading10K: {
		Category:    CategoryTrading,
		DisplayName: "Cumulative trading $10,000",
		Description: "Reach $10,000 in cumulative trading volume",
		Points:      300,
		RequiresKYC: true,
	},
	TaskIDAccumulatedTrading50K: {
		Category:    CategoryTrading,
		DisplayName: "Cumulative trading $50,000",
		Description: "Reach $50,000 in cumulative trading volume",
		Points:      1000,
		RequiresKYC: true,
	},
	TaskIDAccumulatedTrading100K: {
		Category:    CategoryTrading,
		DisplayName: "Cumulative trading $100,000",
		Description: "Reach $100,000 in cumulative trading volume",
		Points:      2500,
		RequiresKYC: true,
	},
	TaskIDAccumulatedTrading500K: {
		Category:    CategoryTrading,
		DisplayName: "Cumulative trading $500,000",
		Description: "Reach $500,000 in cumulative trading volume",
		Points:      10000,
		RequiresKYC: true,
	},
}

// GetTaskDefinition returns definition for a task identifier
func GetTaskDefinition(identifier TaskIdentifier) (TaskDefinition, bool) {
	definition, exists := TaskDefinitionRegistry[identifier]
	return definition, exists
}

// GetTasksByCategory returns all tasks for a given category
func GetTasksByCategory(category TaskCategoryType) []TaskIdentifier {
	var tasks []TaskIdentifier
	for identifier, definition := range TaskDefinitionRegistry {
		if definition.Category == category {
			tasks = append(tasks, identifier)
		}
	}
	return tasks
}

// ValidateTaskEligibility checks if user is eligible for a task
func ValidateTaskEligibility(identifier TaskIdentifier, userTier int, isKYCVerified bool) error {
	definition, exists := GetTaskDefinition(identifier)
	if !exists {
		return fmt.Errorf("task not found: %s", identifier)
	}

	if definition.RequiresKYC && !isKYCVerified {
		return fmt.Errorf("task requires KYC verification")
	}

	if definition.MinTier != nil && userTier < *definition.MinTier {
		return fmt.Errorf("task requires minimum tier %d", *definition.MinTier)
	}

	return nil
}

// GetTaskDisplayName returns the display name for a task identifier
func GetTaskDisplayName(identifier TaskIdentifier) string {
	if definition, exists := TaskDefinitionRegistry[identifier]; exists {
		return definition.DisplayName
	}
	return string(identifier)
}

// IsValidTaskIdentifier checks if a task identifier is valid
func IsValidTaskIdentifier(identifier TaskIdentifier) bool {
	_, exists := TaskDefinitionRegistry[identifier]
	return exists
}
